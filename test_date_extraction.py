#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استخراج التواريخ المحسن
"""

import os
import sys
import json

# إعداد المسارات
project_root = os.path.dirname(__file__)
rasd_media_path = os.path.join(project_root, "rasd_media")
sys.path.insert(0, rasd_media_path)

from scraper.scraper import Scraper
from browser_automation.browser_scraper import BrowserScraper
from rss_generator.rss_generator import RSSGenerator

def test_date_extraction():
    """اختبار استخراج التواريخ من نصوص مختلفة"""
    
    scraper = Scraper("", {})
    
    test_texts = [
        "اليوم الساعة 12:30",
        "منذ ساعتين",
        "أمس في المساء",
        "منذ 3 أيام",
        "2025-08-10",
        "10/08/2025",
        "10-08-2025",
        "10 أغسطس 2025",
        "Aug 10, 2025",
        "الآن",
        "قبل قليل",
        "منذ دقائق",
        "البارحة",
        "منذ يومين",
        "2025/8/10",
        "الساعة 15:30",
        "12:45 ص",
        "نشر في 2025-08-09",
        "تاريخ النشر: اليوم",
        "محدث منذ ساعة"
    ]
    
    print("🧪 اختبار استخراج التواريخ:")
    print("=" * 50)
    
    for text in test_texts:
        result = scraper.extract_date_from_text(text)
        print(f"النص: '{text}' → التاريخ: '{result}'")
    
    print("\n" + "=" * 50)

def test_rss_date_parsing():
    """اختبار تحليل التواريخ في مولد RSS"""
    
    rss = RSSGenerator("اختبار", "http://test.com", "اختبار")
    
    test_dates = [
        "2025-08-10",
        "10/08/2025", 
        "اليوم",
        "أمس",
        "منذ يومين",
        "منذ 3 أيام",
        None,
        "",
        "نص غير صالح"
    ]
    
    print("🧪 اختبار تحليل التواريخ في RSS:")
    print("=" * 50)
    
    for date_str in test_dates:
        result = rss.parse_date(date_str)
        print(f"التاريخ: '{date_str}' → النتيجة: '{result}'")
    
    print("\n" + "=" * 50)

def test_single_source():
    """اختبار مصدر واحد"""
    
    # تحميل المصادر
    sources_file = os.path.join(rasd_media_path, 'sources.json')
    with open(sources_file, 'r', encoding='utf-8') as f:
        sources = json.load(f)
    
    # اختبار أول مصدر
    if sources:
        source = sources[0]
        print(f"🧪 اختبار المصدر: {source['name']}")
        print(f"الرابط: {source['url']}")
        print("=" * 50)
        
        try:
            if source.get('protected'):
                scraper = BrowserScraper(source['url'], source.get('selectors', {}))
            else:
                scraper = Scraper(source['url'], source.get('selectors', {}))
            
            items = scraper.scrape()
            
            print(f"تم جلب {len(items)} خبر")
            
            # عرض أول 5 أخبار مع التواريخ
            for i, item in enumerate(items[:5]):
                print(f"\n{i+1}. العنوان: {item.get('title', 'بدون عنوان')}")
                print(f"   التاريخ: {item.get('date', 'بدون تاريخ')}")
                print(f"   الرابط: {item.get('link', 'بدون رابط')}")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار المصدر: {e}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print("🚀 بدء اختبار استخراج التواريخ المحسن")
    print("=" * 60)
    
    # اختبار استخراج التواريخ
    test_date_extraction()
    
    # اختبار تحليل التواريخ في RSS
    test_rss_date_parsing()
    
    # اختبار مصدر واحد
    test_single_source()
    
    print("✅ انتهى الاختبار")
