import os
import sys
import logging

# إعداد المسارات
project_root = os.path.dirname(__file__)
rasd_media_path = os.path.join(project_root, "rasd_media")

# التأكد من وجود المجلد
if not os.path.exists(rasd_media_path):
    print(f"خطأ: لا يمكن العثور على مجلد rasd_media في {rasd_media_path}")
    sys.exit(1)

# انتقل إلى مجلد المشروع الصحيح
os.chdir(rasd_media_path)
sys.path.insert(0, os.path.abspath("."))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rasd_media.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

try:
    from api.app import app
    print("✅ تم تحميل التطبيق بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)

if __name__ == "__main__":
    print("🚀 بدء تشغيل نظام الرصد الإعلامي...")
    print("📡 الخادم متاح على: http://localhost:5050")
    print("🔧 لوحة الإدارة: http://localhost:5051/admin")

    try:
        app.run(debug=False, port=5050, host='0.0.0.0')
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)
