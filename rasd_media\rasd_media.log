2025-06-15 18:54:02,365 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-15 18:54:02,365 - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 18:54:02,367 - INFO -  * Restarting with stat
2025-06-15 18:54:18,222 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-15 18:54:18,223 - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 18:54:18,225 - INFO -  * Restarting with stat
2025-06-15 18:54:48,350 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-15 18:54:48,350 - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 18:54:52,375 - INFO - 127.0.0.1 - - [15/Jun/2025 18:54:52] "GET / HTTP/1.1" 200 -
2025-06-15 18:54:52,460 - INFO - 127.0.0.1 - - [15/Jun/2025 18:54:52] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 18:55:01,561 - INFO - 127.0.0.1 - - [15/Jun/2025 18:55:01] "GET / HTTP/1.1" 200 -
2025-06-15 18:55:02,738 - INFO - 127.0.0.1 - - [15/Jun/2025 18:55:02] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 19:13:19,385 - INFO - 127.0.0.1 - - [15/Jun/2025 19:13:19] "POST /fetch_news HTTP/1.1" 200 -
2025-06-15 19:13:21,646 - INFO - 127.0.0.1 - - [15/Jun/2025 19:13:21] "GET / HTTP/1.1" 200 -
2025-06-15 19:17:40,366 - INFO - 127.0.0.1 - - [15/Jun/2025 19:17:40] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-15 19:17:40,377 - INFO - 127.0.0.1 - - [15/Jun/2025 19:17:40] "GET / HTTP/1.1" 200 -
2025-06-15 19:17:42,351 - INFO - 127.0.0.1 - - [15/Jun/2025 19:17:42] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:18,434 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:18] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:21,753 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:21] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:22,760 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:22] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:23,546 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:23] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:24,438 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:24] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:25,182 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:25] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:25,872 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:25] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:26,465 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:26] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:26,877 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:26] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:27,638 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:27] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:28,330 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:28] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:29,583 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:29] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:30,428 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:30] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:31,171 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:31] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:32,646 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:32] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:33,540 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:33] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:34,318 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:34] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:34,937 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:34] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:35,695 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:35] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:36,422 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:36] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:37,240 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:37] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:37,938 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:37] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:38,886 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:38] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:39,630 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:39] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:40,457 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:40] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:41,273 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:41] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:18:59,808 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:59] "[32mPOST /admin/edit/18 HTTP/1.1[0m" 302 -
2025-06-15 19:18:59,836 - INFO - 127.0.0.1 - - [15/Jun/2025 19:18:59] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:19:06,674 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:06] "[32mPOST /admin/edit/0 HTTP/1.1[0m" 302 -
2025-06-15 19:19:06,707 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:06] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:19:07,742 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:07] "[32mPOST /admin/edit/0 HTTP/1.1[0m" 302 -
2025-06-15 19:19:07,767 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:07] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:19:08,875 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:08] "[32mPOST /admin/edit/0 HTTP/1.1[0m" 302 -
2025-06-15 19:19:08,901 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:08] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:19:10,312 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:10] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:19:11,025 - INFO - 127.0.0.1 - - [15/Jun/2025 19:19:11] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:20:11,083 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-15 19:20:11,083 - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 19:20:15,297 - INFO - 127.0.0.1 - - [15/Jun/2025 19:20:15] "GET / HTTP/1.1" 200 -
2025-06-15 19:20:15,438 - INFO - 127.0.0.1 - - [15/Jun/2025 19:20:15] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 19:20:19,439 - INFO - 127.0.0.1 - - [15/Jun/2025 19:20:19] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:20:25,885 - INFO - 127.0.0.1 - - [15/Jun/2025 19:20:25] "GET /admin HTTP/1.1" 200 -
2025-06-15 19:38:13,011 - INFO - 127.0.0.1 - - [15/Jun/2025 19:38:13] "POST /admin/test_all HTTP/1.1" 200 -
2025-06-15 19:58:24,360 - INFO - 127.0.0.1 - - [15/Jun/2025 19:58:24] "POST /admin/test_all HTTP/1.1" 200 -
2025-06-15 21:59:54,221 - INFO - 127.0.0.1 - - [15/Jun/2025 21:59:54] "GET / HTTP/1.1" 200 -
2025-06-18 16:43:49,434 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-18 16:43:49,434 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 16:43:52,917 - INFO - 127.0.0.1 - - [18/Jun/2025 16:43:52] "GET / HTTP/1.1" 200 -
2025-06-18 16:43:53,285 - INFO - 127.0.0.1 - - [18/Jun/2025 16:43:53] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 16:43:58,442 - INFO - 127.0.0.1 - - [18/Jun/2025 16:43:58] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-18 16:43:58,458 - INFO - 127.0.0.1 - - [18/Jun/2025 16:43:58] "GET / HTTP/1.1" 200 -
2025-06-18 16:55:25,899 - INFO - 127.0.0.1 - - [18/Jun/2025 16:55:25] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 16:55:27,893 - INFO - 127.0.0.1 - - [18/Jun/2025 16:55:27] "GET / HTTP/1.1" 200 -
2025-06-18 16:55:56,714 - INFO - 127.0.0.1 - - [18/Jun/2025 16:55:56] "GET /admin HTTP/1.1" 200 -
2025-06-18 16:56:02,331 - INFO - 127.0.0.1 - - [18/Jun/2025 16:56:02] "GET / HTTP/1.1" 200 -
2025-06-18 17:07:52,801 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-06-18 17:07:52,801 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:10:36,791 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 17:10:36,791 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:10:39,666 - INFO - 127.0.0.1 - - [18/Jun/2025 17:10:39] "GET / HTTP/1.1" 200 -
2025-06-18 17:10:40,136 - INFO - 127.0.0.1 - - [18/Jun/2025 17:10:40] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:10:43,390 - INFO - 127.0.0.1 - - [18/Jun/2025 17:10:43] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-18 17:10:43,705 - INFO - 127.0.0.1 - - [18/Jun/2025 17:10:43] "GET / HTTP/1.1" 200 -
2025-06-18 17:12:36,482 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 17:12:36,483 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:12:36,552 - INFO - 127.0.0.1 - - [18/Jun/2025 17:12:36] "GET / HTTP/1.1" 200 -
2025-06-18 17:12:36,617 - INFO - 127.0.0.1 - - [18/Jun/2025 17:12:36] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:14:01,548 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 17:14:01,550 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:14:02,133 - INFO - 127.0.0.1 - - [18/Jun/2025 17:14:02] "GET / HTTP/1.1" 200 -
2025-06-18 17:14:02,195 - INFO - 127.0.0.1 - - [18/Jun/2025 17:14:02] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:17:33,651 - INFO - 127.0.0.1 - - [18/Jun/2025 17:17:33] "GET / HTTP/1.1" 200 -
2025-06-18 17:27:18,246 - INFO - 127.0.0.1 - - [18/Jun/2025 17:27:18] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 17:27:20,159 - INFO - 127.0.0.1 - - [18/Jun/2025 17:27:20] "GET / HTTP/1.1" 200 -
2025-06-18 17:28:15,659 - INFO - 127.0.0.1 - - [18/Jun/2025 17:28:15] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-18 17:28:15,659 - INFO - 127.0.0.1 - - [18/Jun/2025 17:28:15] "GET / HTTP/1.1" 200 -
2025-06-18 17:29:20,647 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 17:29:20,647 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:29:23,063 - INFO - 127.0.0.1 - - [18/Jun/2025 17:29:23] "GET / HTTP/1.1" 200 -
2025-06-18 17:29:23,220 - INFO - 127.0.0.1 - - [18/Jun/2025 17:29:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:41:18,383 - INFO - 127.0.0.1 - - [18/Jun/2025 17:41:18] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 17:41:20,707 - INFO - 127.0.0.1 - - [18/Jun/2025 17:41:20] "GET / HTTP/1.1" 200 -
2025-06-18 17:44:27,492 - INFO - 127.0.0.1 - - [18/Jun/2025 17:44:27] "GET / HTTP/1.1" 200 -
2025-06-18 17:44:29,353 - INFO - 127.0.0.1 - - [18/Jun/2025 17:44:29] "GET / HTTP/1.1" 200 -
2025-06-18 17:44:47,857 - INFO - 127.0.0.1 - - [18/Jun/2025 17:44:47] "GET / HTTP/1.1" 200 -
2025-06-18 17:44:47,950 - INFO - 127.0.0.1 - - [18/Jun/2025 17:44:47] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:45:41,310 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 17:45:41,310 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 17:45:43,389 - INFO - 127.0.0.1 - - [18/Jun/2025 17:45:43] "GET / HTTP/1.1" 200 -
2025-06-18 17:45:43,483 - INFO - 127.0.0.1 - - [18/Jun/2025 17:45:43] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 17:55:59,563 - INFO - 127.0.0.1 - - [18/Jun/2025 17:55:59] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 17:56:01,533 - INFO - 127.0.0.1 - - [18/Jun/2025 17:56:01] "GET / HTTP/1.1" 200 -
2025-06-18 18:07:19,112 - INFO - 127.0.0.1 - - [18/Jun/2025 18:07:19] "GET / HTTP/1.1" 200 -
2025-06-18 18:09:05,160 - INFO - 127.0.0.1 - - [18/Jun/2025 18:09:05] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-18 18:09:05,176 - INFO - 127.0.0.1 - - [18/Jun/2025 18:09:05] "GET / HTTP/1.1" 200 -
2025-06-18 18:10:53,159 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 18:10:53,159 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 18:10:54,133 - INFO - 127.0.0.1 - - [18/Jun/2025 18:10:54] "GET / HTTP/1.1" 200 -
2025-06-18 18:10:55,372 - INFO - 127.0.0.1 - - [18/Jun/2025 18:10:55] "GET / HTTP/1.1" 200 -
2025-06-18 18:10:55,906 - INFO - 127.0.0.1 - - [18/Jun/2025 18:10:55] "GET / HTTP/1.1" 200 -
2025-06-18 18:10:57,532 - INFO - 127.0.0.1 - - [18/Jun/2025 18:10:57] "GET /admin HTTP/1.1" 200 -
2025-06-18 18:10:59,318 - INFO - 127.0.0.1 - - [18/Jun/2025 18:10:59] "GET / HTTP/1.1" 200 -
2025-06-18 18:22:05,729 - INFO - 127.0.0.1 - - [18/Jun/2025 18:22:05] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 18:22:07,531 - INFO - 127.0.0.1 - - [18/Jun/2025 18:22:07] "GET / HTTP/1.1" 200 -
2025-06-18 18:35:19,422 - INFO - 127.0.0.1 - - [18/Jun/2025 18:35:19] "GET / HTTP/1.1" 200 -
2025-06-18 18:35:38,477 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 18:35:38,477 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 18:35:42,767 - INFO - 127.0.0.1 - - [18/Jun/2025 18:35:42] "GET / HTTP/1.1" 200 -
2025-06-18 18:35:55,201 - INFO - 127.0.0.1 - - [18/Jun/2025 18:35:55] "GET / HTTP/1.1" 200 -
2025-06-18 18:35:55,338 - INFO - 127.0.0.1 - - [18/Jun/2025 18:35:55] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 18:44:26,153 - INFO - 127.0.0.1 - - [18/Jun/2025 18:44:26] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 18:44:27,562 - INFO - 127.0.0.1 - - [18/Jun/2025 18:44:27] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:10,123 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:10] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:10,880 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:10] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:11,070 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:11] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:11,219 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:11] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:11,403 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:11] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:11,586 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:11] "GET / HTTP/1.1" 200 -
2025-06-18 18:51:11,782 - INFO - 127.0.0.1 - - [18/Jun/2025 18:51:11] "GET / HTTP/1.1" 200 -
2025-06-18 18:54:16,276 - INFO - 127.0.0.1 - - [18/Jun/2025 18:54:16] "GET / HTTP/1.1" 200 -
2025-06-18 18:55:05,174 - INFO - 127.0.0.1 - - [18/Jun/2025 18:55:05] "GET / HTTP/1.1" 200 -
2025-06-18 18:55:14,636 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 18:55:14,636 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 18:55:16,996 - INFO - 127.0.0.1 - - [18/Jun/2025 18:55:16] "GET / HTTP/1.1" 200 -
2025-06-18 18:55:17,084 - INFO - 127.0.0.1 - - [18/Jun/2025 18:55:17] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 19:01:04,537 - INFO - 127.0.0.1 - - [18/Jun/2025 19:01:04] "GET / HTTP/1.1" 200 -
2025-06-18 19:11:17,353 - INFO - 127.0.0.1 - - [18/Jun/2025 19:11:17] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 19:11:19,869 - INFO - 127.0.0.1 - - [18/Jun/2025 19:11:19] "GET / HTTP/1.1" 200 -
2025-06-18 19:11:49,009 - INFO - 127.0.0.1 - - [18/Jun/2025 19:11:49] "GET / HTTP/1.1" 200 -
2025-06-18 19:20:58,973 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-18 19:20:58,973 - INFO - [33mPress CTRL+C to quit[0m
2025-06-18 19:21:01,380 - INFO - 127.0.0.1 - - [18/Jun/2025 19:21:01] "GET / HTTP/1.1" 200 -
2025-06-18 19:21:01,451 - INFO - 127.0.0.1 - - [18/Jun/2025 19:21:01] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-18 19:21:05,840 - INFO - 127.0.0.1 - - [18/Jun/2025 19:21:05] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-18 19:21:06,162 - INFO - 127.0.0.1 - - [18/Jun/2025 19:21:06] "GET / HTTP/1.1" 200 -
2025-06-18 19:30:02,319 - INFO - 127.0.0.1 - - [18/Jun/2025 19:30:02] "POST /fetch_news HTTP/1.1" 200 -
2025-06-18 19:30:04,865 - INFO - 127.0.0.1 - - [18/Jun/2025 19:30:04] "GET / HTTP/1.1" 200 -
2025-06-18 19:37:03,972 - INFO - 127.0.0.1 - - [18/Jun/2025 19:37:03] "GET / HTTP/1.1" 200 -
2025-06-28 17:47:35,327 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-06-28 17:47:35,328 - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 17:47:35,730 - INFO - 127.0.0.1 - - [28/Jun/2025 17:47:35] "GET / HTTP/1.1" 200 -
2025-06-28 17:47:35,882 - INFO - 127.0.0.1 - - [28/Jun/2025 17:47:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-28 18:00:18,071 - INFO - 127.0.0.1 - - [28/Jun/2025 18:00:18] "POST /fetch_news HTTP/1.1" 200 -
2025-06-28 18:00:19,867 - INFO - 127.0.0.1 - - [28/Jun/2025 18:00:19] "GET / HTTP/1.1" 200 -
2025-06-28 18:00:56,305 - INFO - 127.0.0.1 - - [28/Jun/2025 18:00:56] "[32mPOST /delete_all_feeds HTTP/1.1[0m" 302 -
2025-06-28 18:00:56,314 - INFO - 127.0.0.1 - - [28/Jun/2025 18:00:56] "GET / HTTP/1.1" 200 -
2025-08-10 20:54:42,067 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-08-10 20:54:42,067 - INFO - [33mPress CTRL+C to quit[0m
2025-08-10 20:54:48,531 - INFO - ************* - - [10/Aug/2025 20:54:48] "GET / HTTP/1.1" 200 -
2025-08-10 20:54:48,972 - INFO - ************* - - [10/Aug/2025 20:54:48] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-10 20:55:00,000 - INFO - ************* - - [10/Aug/2025 20:55:00] "GET /admin HTTP/1.1" 200 -
2025-08-10 20:55:13,127 - INFO - ************* - - [10/Aug/2025 20:55:13] "GET / HTTP/1.1" 200 -
2025-08-10 21:10:55,721 - INFO - ************* - - [10/Aug/2025 21:10:55] "POST /fetch_news HTTP/1.1" 200 -
2025-08-10 21:10:58,185 - INFO - ************* - - [10/Aug/2025 21:10:58] "GET / HTTP/1.1" 200 -
2025-08-10 21:19:46,446 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5050
 * Running on http://*************:5050
2025-08-10 21:19:46,446 - INFO - [33mPress CTRL+C to quit[0m
2025-08-10 21:20:11,467 - INFO - 127.0.0.1 - - [10/Aug/2025 21:20:11] "GET / HTTP/1.1" 200 -
2025-08-10 21:20:11,974 - INFO - 127.0.0.1 - - [10/Aug/2025 21:20:11] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
