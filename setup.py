#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد نظام الرصد الإعلامي
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ يتطلب Python 3.8 أو أحدث. الإصدار الحالي: {version.major}.{version.minor}")
        return False
    
    print(f"✅ إصدار Python مناسب: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    requirements_file = os.path.join("rasd_media", "requirements.txt")
    
    if not os.path.exists(requirements_file):
        print(f"❌ ملف المتطلبات غير موجود: {requirements_file}")
        return False
    
    # تحديث pip أولاً
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "تحديث pip"):
        print("⚠️  فشل في تحديث pip، سنتابع بالإصدار الحالي")
    
    # تثبيت المتطلبات
    return run_command(
        f"{sys.executable} -m pip install -r {requirements_file}",
        "تثبيت المتطلبات"
    )

def setup_playwright():
    """إعداد Playwright"""
    print("🔄 إعداد Playwright...")
    
    # تثبيت متصفحات Playwright
    commands = [
        f"{sys.executable} -m playwright install chromium",
        f"{sys.executable} -m playwright install-deps"
    ]
    
    for command in commands:
        if not run_command(command, f"تشغيل: {command}"):
            print("⚠️  فشل في إعداد Playwright، قد تحتاج لتشغيله يدوياً")
            return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("🔄 إنشاء المجلدات...")
    
    directories = [
        "rasd_media/feeds",
        "rasd_media/logs"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ تم إنشاء المجلد: {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء المجلد {directory}: {e}")
            return False
    
    return True

def check_sources_file():
    """فحص ملف المصادر"""
    sources_file = os.path.join("rasd_media", "sources.json")
    
    if not os.path.exists(sources_file):
        print(f"❌ ملف المصادر غير موجود: {sources_file}")
        return False
    
    try:
        import json
        with open(sources_file, 'r', encoding='utf-8') as f:
            sources = json.load(f)
        
        print(f"✅ ملف المصادر صحيح - يحتوي على {len(sources)} مصدر")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ملف المصادر: {e}")
        return False

def test_installation():
    """اختبار التثبيت"""
    print("🧪 اختبار التثبيت...")
    
    try:
        # تغيير المجلد للاختبار
        original_dir = os.getcwd()
        os.chdir(os.path.join(original_dir, "rasd_media"))
        sys.path.insert(0, os.path.abspath("."))
        
        # اختبار استيراد الوحدات الأساسية
        from scraper.scraper import Scraper
        from rss_generator.rss_generator import RSSGenerator
        print("✅ تم استيراد الوحدات الأساسية")
        
        # اختبار Flask
        from api.app import app
        print("✅ تم استيراد تطبيق Flask")
        
        # العودة للمجلد الأصلي
        os.chdir(original_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار التثبيت: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🚀 بدء إعداد نظام الرصد الإعلامي")
    print("=" * 50)
    
    # فحص النظام
    if not check_python_version():
        return False
    
    print(f"💻 نظام التشغيل: {platform.system()} {platform.release()}")
    
    # خطوات الإعداد
    steps = [
        ("إنشاء المجلدات", create_directories),
        ("فحص ملف المصادر", check_sources_file),
        ("تثبيت المتطلبات", install_requirements),
        ("إعداد Playwright", setup_playwright),
        ("اختبار التثبيت", test_installation),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"❌ فشل في: {step_name}")
            print("⚠️  يرجى إصلاح الأخطاء أعلاه قبل المتابعة")
            return False
    
    print("\n" + "="*50)
    print("🎉 تم إعداد النظام بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("   1. تشغيل النظام: python run.py")
    print("   2. فتح المتصفح على: http://localhost:5050")
    print("   3. الذهاب لصفحة الإدارة: http://localhost:5050/admin")
    print("   4. اختبار جلب الأخبار من الواجهة")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 هل تريد تشغيل اختبار شامل للنظام؟ (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes', 'نعم', 'ن']:
                print("\n🧪 تشغيل الاختبار الشامل...")
                os.system(f"{sys.executable} test_system.py")
        except KeyboardInterrupt:
            print("\n👋 تم إلغاء الاختبار")
    
    sys.exit(0 if success else 1)
